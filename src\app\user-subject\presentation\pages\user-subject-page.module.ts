import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { UserSubjectPageRoutingModule } from './user-subject-page-routing.module';
import { SubjectGeneralPageComponent } from './subject-general-page/subject-general-page.component';
import { UserGeneralPageComponent } from './user-general-page/user-general-page.component';
import { DialogModule } from 'primeng/dialog';
import { LoadingSpinnerModule } from 'src/verazial-common-frontend/modules/shared/components/loading-spinner/loading-spinner.module';
import { ToastModule } from 'primeng/toast';
import { EmptyModule } from 'src/verazial-common-frontend/modules/shared/components/empty/empty.module';
import { ListUserSubjectModule } from '../components/list-user-subject/list-user-subject.module';
import { UserSubjectEditPageComponent } from './user-subject-edit-page/user-subject-edit-page.component';
import { UserSubjectCardModule } from '../components/user-subject-card/user-subject-card.module';
import { CardModule } from 'primeng/card';
import { TabViewModule } from 'primeng/tabview';
import { TranslateModule } from '@ngx-translate/core';
import { ButtonModule } from 'primeng/button';
import { WidgetEnrollModule } from 'src/verazial-common-frontend/modules/shared/components/widget-enroll/widget-enroll.module';
import { WidgetMatchModule } from 'src/verazial-common-frontend/modules/shared/components/widget-match/widget-match.module';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { DynamicFormModule } from '../components/dynamic-form/dynamic-form.module';
import { DropdownModule } from 'primeng/dropdown';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { PhysicalDataModule } from '../components/physical-data/physical-data.module';
import { PicHistoryModule } from '../components/pic-history/pic-history.module';
import { UserNotVerifiedModule } from 'src/verazial-common-frontend/modules/shared/components/user-not-verified/user-not-verified.module';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { CheckboxModule } from 'primeng/checkbox';
import { MultiSelectModule } from 'primeng/multiselect';
import { PasswordModule } from 'primeng/password';
import { InputTextModule } from 'primeng/inputtext';
import { TooltipModule } from 'primeng/tooltip';
import { EnrollSubjectPageComponent } from './enroll-subject-page/enroll-subject-page.component';
import { AccordionModule } from 'primeng/accordion';
import { StepperModule } from 'primeng/stepper';
import { EditUserSubjectModule } from 'src/verazial-common-frontend/modules/shared/components/edit-user-subject/edit-user-subject.module';
import { UserLocationsModule } from '../components/user-locations/user-locations.module';
import { RelatedSubjectsModule } from '../components/related-subjects/related-subjects.module';
import { BelongingsModule } from 'src/app/prisons/presentation/components/belongings/belongings.module';
import { JudicialFilesModule } from 'src/app/prisons/presentation/components/judicial-files/judicial-files.module';
import { EntriesExitsModule } from '../components/entries-exits/entries-exits.module';
import { EntryExitAuthsModule } from '../components/entry-exit-auths/entry-exit-auths.module';
import { StepsModule } from 'primeng/steps';
import { SubjectFilesModule } from '../components/subjcet-files/subject-files.module';
import { NewSelectOptionModule } from 'src/verazial-common-frontend/modules/shared/components/new-select-option/new-select-option.module';
import { FileUploadModule } from 'primeng/fileupload';
import { UiFingerprintDetectorModule } from 'ngx-fingerprint-detector';
import { TableModule } from 'primeng/table';

@NgModule({
  declarations: [
    SubjectGeneralPageComponent,
    UserGeneralPageComponent,
    UserSubjectEditPageComponent,
    EnrollSubjectPageComponent,
  ],
  imports: [
    /* Routing */
    UserSubjectPageRoutingModule,
    /* Angular Modules */
    CommonModule,
    /* Form Modules */
    FormsModule,
    ReactiveFormsModule,
    /* Translate */
    TranslateModule,
    /* Prime NG Modules */
    DialogModule,
    ToastModule,
    CardModule,
    TabViewModule,
    ButtonModule,
    ConfirmDialogModule,
    DropdownModule,
    ProgressSpinnerModule,
    CheckboxModule,
    MultiSelectModule,
    PasswordModule,
    InputTextModule,
    TooltipModule,
    AccordionModule,
    StepperModule,
    StepsModule,
    FileUploadModule,
    TableModule,
    /* Prisons */
    BelongingsModule,
    JudicialFilesModule,
    EntryExitAuthsModule,
    /* Custom Modules */
    LoadingSpinnerModule,
    EmptyModule,
    ListUserSubjectModule,
    UserSubjectCardModule,
    WidgetEnrollModule,
    WidgetMatchModule,
    EditUserSubjectModule,
    DynamicFormModule,
    PhysicalDataModule,
    PicHistoryModule,
    UserNotVerifiedModule,
    RelatedSubjectsModule,
    UserLocationsModule,
    EntriesExitsModule,
    SubjectFilesModule,
    NewSelectOptionModule,
    UiFingerprintDetectorModule,
  ],
  exports:[
    SubjectGeneralPageComponent,
    UserGeneralPageComponent,
    UserSubjectEditPageComponent,
    EnrollSubjectPageComponent,
  ]
})
export class UserSubjectPageModule { }
