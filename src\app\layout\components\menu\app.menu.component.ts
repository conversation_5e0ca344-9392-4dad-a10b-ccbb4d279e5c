import {Component, OnInit} from '@angular/core';
import {LayoutService} from '../../service/app.layout.service';
import {
    RoleAccessResponseEntity
} from 'src/verazial-common-frontend/core/general/role/domain/entity/role-access-response.entity';
import {CheckPermissionsService} from 'src/verazial-common-frontend/core/services/check-permissions-service';
import {AccessIdentifier} from 'src/verazial-common-frontend/core/models/access-identifier.enum';
import {environment} from 'src/environments/environment';
import {LocalStorageService} from 'src/verazial-common-frontend/core/services/local-storage.service';
import {
    GetKonektorPropertiesUseCase
} from 'src/verazial-common-frontend/core/general/konektor/domain/use-cases/get-konektor-properties.use-case';
import {
    GetSettingsByApplicationUseCase
} from 'src/verazial-common-frontend/core/general/manager/domain/use-cases/get-settings-by-application.use-case';
import {
    GetAllRolesUseCase
} from 'src/verazial-common-frontend/core/general/role/domain/use-cases/roles/get-all-roles.use-case';
import {RoleType} from 'src/verazial-common-frontend/core/general/role/common/enum/role-type.enum';
import {TranslateService} from '@ngx-translate/core';
import {ConsoleLoggerService} from 'src/verazial-common-frontend/core/services/console-logger.service';
import {MenuItem} from "primeng/api";
import { ActivatedRoute } from '@angular/router';

@Component({
    selector: 'app-menu',
    templateUrl: './app.menu.component.html'
})
export class AppMenuComponent implements OnInit {

    model: MenuItem[] = [];
    roleAccesses: RoleAccessResponseEntity[] = [];
    version: string = environment.version;

    showSelectOptionDialog: boolean = false;

    constructor(
        public layoutService: LayoutService,
        private route: ActivatedRoute,
        private checkPermissionsService: CheckPermissionsService,
        private localStorageService: LocalStorageService,
        private getKonektorPropertiesUseCase: GetKonektorPropertiesUseCase,
        private getSettingsByApplicationUseCase: GetSettingsByApplicationUseCase,
        private getAllRolesUseCase: GetAllRolesUseCase,
        private translateService: TranslateService,
        private loggerService: ConsoleLoggerService,
    ) {
    }

    ngOnInit() {
        this.model = [];
        this.getKonektorPropertiesUseCase.execute().subscribe({
            next: (data) => {
                const konektorProperties = data;
                if (data.apiGatewayGrpc) {
                    this.localStorageService.setApiGatewayURL(data.apiGatewayGrpc);
                }
                else {
                    this.localStorageService.destroyApiGatewayURL();
                }
                this.getSettingsByApplicationUseCase.execute({ applicationName: environment.application }).then(
                    (data) => {
                        const managerSettings = data.settings;
                        if (managerSettings) this.localStorageService.setSessionSettings(managerSettings);
                        if (this.checkPermissionsService.hasReadPermissions(AccessIdentifier.HOME)) {
                            const home_menu = {
                                label: '',
                                accessCode: AccessIdentifier.HOME,
                                items: [{}]
                            }
                            if (this.checkPermissionsService.hasReadPermissions(AccessIdentifier.HOME)) home_menu.items.push(
                                {
                                    label: 'menu.home',
                                    accessCode: AccessIdentifier.HOME,
                                    icon: 'pi pi-home',
                                    routerLink: ['/home'],
                                    routerLinks: ['/home']
                                },
                            );
                            this.model.push(
                                home_menu,
                                {
                                    separator: true,
                                }
                            );
                        }
                        if (this.checkPermissionsService.hasReadPermissions(AccessIdentifier.GENERAL_USER_SUBJECT_MANAGEMENT)) {
                            const subject_menu = {
                                label: '',
                                accessCode: AccessIdentifier.GENERAL_USER_SUBJECT_MANAGEMENT,
                                items: [{}],
                            }
                            if (this.checkPermissionsService.hasReadPermissions(AccessIdentifier.SUBJECT_BY_ROLES)) {
                                const subjects_by_role_menu: MenuItem = {
                                    label: 'menu.subjects',
                                    accessCode: AccessIdentifier.SUBJECT_BY_ROLES,
                                    icon: 'pi pi-id-card',
                                    items: [{
                                        label: 'menu.all_m',
                                        accessCode: AccessIdentifier.SUBJECT_BY_ROLES,
                                        icon: 'pi pi-users',
                                        routerLink: ['/general/subject/'],
                                        routerLinks: ['/general/subject/']
                                    }]
                                };
                                this.getAllRolesUseCase.execute().then(
                                    (data) => {
                                        // filter roles by level
                                        data.sort((a, b) => (a.level ?? 0) - (b.level ?? 0));
                                        for (const role of data.filter((role) => role.type == RoleType.SUBJECT)) {
                                            if (role.showInMenu) {
                                                const subjectRoleMenu = {
                                                    label: role.name == 'SYSTEM_USER' ? this.translateService.instant('role_names.SYSTEM_USER') : role.name,
                                                    accessCode: AccessIdentifier.SUBJECT_BY_ROLES,
                                                    icon: 'NONE',
                                                    routerLink: ['/general/subjects/' + role.id],
                                                    routerLinks: ['/general/subjects/' + role.id]
                                                };
                                                subjects_by_role_menu.items!.push(subjectRoleMenu);
                                            }
                                        }
                                    },
                                    (e) => {
                                        this.loggerService.error('Error Getting Roles:');
                                        this.loggerService.error(e);
                                    }
                                ).finally(() => {
                                    subject_menu.items.push(subjects_by_role_menu);
                                });
                            } else if (this.checkPermissionsService.hasReadPermissions(AccessIdentifier.SUBJECT)) subject_menu.items.push(
                                {
                                    label: 'menu.subject',
                                    accessCode: AccessIdentifier.SUBJECT,
                                    icon: 'pi pi-id-card',
                                    routerLink: ['/general/subject'],
                                    routerLinks: ['/general/subject', '/general/subject/edit/']
                                }
                            );
                            if (this.checkPermissionsService.hasReadPermissions(AccessIdentifier.ENROLL_WIZARD)) {
                                // const new_subject_enroll_menu: MenuItem = {
                                //     label: 'menu.new_subject',
                                //     accessCode: AccessIdentifier.ENROLL_WIZARD,
                                //     iconImage: 'verazial-common-frontend/assets/images/menu/NewEnroll.svg',
                                //     iconImageActive: 'verazial-common-frontend/assets/images/menu/NewEnrollActive.svg',
                                //     iconImageDisabled: 'verazial-common-frontend/assets/images/menu/NewEnrollDisabled.svg',
                                //     command: () => {
                                //         this.openSelector();
                                //     }
                                // };
                                // subject_menu.items.push(new_subject_enroll_menu);
                                const new_subject_menu: MenuItem = {
                                    label: 'menu.new_subject',
                                    accessCode: AccessIdentifier.ENROLL_WIZARD,
                                    iconImage: 'verazial-common-frontend/assets/images/menu/NewEnroll.svg',
                                    iconImageActive: 'verazial-common-frontend/assets/images/menu/NewEnrollActive.svg',
                                    iconImageDisabled: 'verazial-common-frontend/assets/images/menu/NewEnrollDisabled.svg',
                                    items: [{
                                        label: 'menu.biographic_data',
                                        stepper: true,
                                        step: 'new',
                                        accessCode: AccessIdentifier.ENROLL_WIZARD,
                                        iconImage: 'verazial-common-frontend/assets/images/menu/EditPending.svg',
                                        iconImageActive: 'verazial-common-frontend/assets/images/menu/EditActive.svg',
                                        iconImageCompleted: 'verazial-common-frontend/assets/images/menu/EditCompleted.svg',
                                        iconImageCompletedDisabled: 'verazial-common-frontend/assets/images/menu/EditCompletedDisabled.svg',
                                        iconImageDisabled: 'verazial-common-frontend/assets/images/menu/EditPendingDisabled.svg',
                                        routerLink: ['/general/enroll/new'],
                                        routerLinks: ['/general/enroll/new']
                                    }]
                                };
                                // if (managerSettings?.continued1?.criminalisticsSettings?.isEnrollByFileEnabled!) {
                                //     new_subject_menu.items!.push({
                                //             label: 'menu.fingerprint_form',
                                //             stepper: true,
                                //             step: 'file',
                                //             accessCode: AccessIdentifier.ENROLL_WIZARD,
                                //             iconImage: 'verazial-common-frontend/assets/images/menu/EditPending.svg',
                                //             iconImageActive: 'verazial-common-frontend/assets/images/menu/EditActive.svg',
                                //             iconImageCompleted: 'verazial-common-frontend/assets/images/menu/EditCompleted.svg',
                                //             iconImageCompletedDisabled: 'verazial-common-frontend/assets/images/menu/EditCompletedDisabled.svg',
                                //             iconImageDisabled: 'verazial-common-frontend/assets/images/menu/EditPendingDisabled.svg',
                                //             routerLink: ['/general/enroll/file'],
                                //             routerLinks: ['/general/enroll/file']
                                //         });
                                // }
                                if (managerSettings?.payedTechnology?.iris == true && konektorProperties.enabledTech?.iris == true) {
                                    new_subject_menu.items!.push({
                                        label: 'menu.iris',
                                        stepper: true,
                                        step: 'iris',
                                        accessCode: AccessIdentifier.ENROLL_WIZARD,
                                        iconImage: 'verazial-common-frontend/assets/images/menu/EditPending.svg',
                                        iconImageActive: 'verazial-common-frontend/assets/images/menu/EditActive.svg',
                                        iconImageCompleted: 'verazial-common-frontend/assets/images/menu/EditCompleted.svg',
                                        iconImageCompletedDisabled: 'verazial-common-frontend/assets/images/menu/EditCompletedDisabled.svg',
                                        iconImageDisabled: 'verazial-common-frontend/assets/images/menu/EditPendingDisabled.svg',
                                        routerLink: ['/general/enroll/iris'],
                                        routerLinks: ['/general/enroll/iris']
                                    });
                                }
                                if (managerSettings?.payedTechnology?.dactilar == true && konektorProperties.enabledTech?.dactilar == true) {
                                    new_subject_menu.items!.push({
                                        label: 'menu.fingerprint',
                                        stepper: true,
                                        step: 'fingerprint',
                                        accessCode: AccessIdentifier.ENROLL_WIZARD,
                                        iconImage: 'verazial-common-frontend/assets/images/menu/EditPending.svg',
                                        iconImageActive: 'verazial-common-frontend/assets/images/menu/EditActive.svg',
                                        iconImageCompleted: 'verazial-common-frontend/assets/images/menu/EditCompleted.svg',
                                        iconImageCompletedDisabled: 'verazial-common-frontend/assets/images/menu/EditCompletedDisabled.svg',
                                        iconImageDisabled: 'verazial-common-frontend/assets/images/menu/EditPendingDisabled.svg',
                                        routerLink: ['/general/enroll/fingerprint'],
                                        routerLinks: ['/general/enroll/fingerprint']
                                    });
                                }
                                if (managerSettings?.payedTechnology?.facial == true && konektorProperties.enabledTech?.facial == true) {
                                    new_subject_menu.items!.push({
                                        label: 'menu.facial',
                                        stepper: true,
                                        step: 'facial',
                                        accessCode: AccessIdentifier.ENROLL_WIZARD,
                                        iconImage: 'verazial-common-frontend/assets/images/menu/EditPending.svg',
                                        iconImageActive: 'verazial-common-frontend/assets/images/menu/EditActive.svg',
                                        iconImageCompleted: 'verazial-common-frontend/assets/images/menu/EditCompleted.svg',
                                        iconImageCompletedDisabled: 'verazial-common-frontend/assets/images/menu/EditCompletedDisabled.svg',
                                        iconImageDisabled: 'verazial-common-frontend/assets/images/menu/EditPendingDisabled.svg',
                                        routerLink: ['/general/enroll/facial'],
                                        routerLinks: ['/general/enroll/facial']
                                    });
                                }
                                if (managerSettings?.payedTechnology?.dactilarRolled == true && konektorProperties.enabledTech?.dactilarRolled == true) {
                                    new_subject_menu.items!.push({
                                        label: 'menu.rolledFingerPrint',
                                        stepper: true,
                                        step: 'fingerprintRolled',
                                        accessCode: AccessIdentifier.ENROLL_WIZARD,
                                        iconImage: 'verazial-common-frontend/assets/images/menu/EditPending.svg',
                                        iconImageActive: 'verazial-common-frontend/assets/images/menu/EditActive.svg',
                                        iconImageCompleted: 'verazial-common-frontend/assets/images/menu/EditCompleted.svg',
                                        iconImageCompletedDisabled: 'verazial-common-frontend/assets/images/menu/EditCompletedDisabled.svg',
                                        iconImageDisabled: 'verazial-common-frontend/assets/images/menu/EditPendingDisabled.svg',
                                        routerLink: ['/general/enroll/fingerprintRolled'],
                                        routerLinks: ['/general/enroll/fingerprintRolled']
                                    });
                                }
                                if (managerSettings?.payedTechnology?.palm == true && konektorProperties.enabledTech?.palm == true) {
                                    new_subject_menu.items!.push({
                                        label: 'menu.palm',
                                        stepper: true,
                                        step: 'palm',
                                        accessCode: AccessIdentifier.ENROLL_WIZARD,
                                        iconImage: 'verazial-common-frontend/assets/images/menu/EditPending.svg',
                                        iconImageActive: 'verazial-common-frontend/assets/images/menu/EditActive.svg',
                                        iconImageCompleted: 'verazial-common-frontend/assets/images/menu/EditCompleted.svg',
                                        iconImageCompletedDisabled: 'verazial-common-frontend/assets/images/menu/EditCompletedDisabled.svg',
                                        iconImageDisabled: 'verazial-common-frontend/assets/images/menu/EditPendingDisabled.svg',
                                        routerLink: ['/general/enroll/palm'],
                                        routerLinks: ['/general/enroll/palm']
                                    });
                                }
                                new_subject_menu.items!.push({
                                    label: 'menu.additional_info',
                                    stepper: true,
                                    step: 'continued',
                                    accessCode: AccessIdentifier.ENROLL_WIZARD,
                                    iconImage: 'verazial-common-frontend/assets/images/menu/EditPending.svg',
                                    iconImageActive: 'verazial-common-frontend/assets/images/menu/EditActive.svg',
                                    iconImageCompleted: 'verazial-common-frontend/assets/images/menu/EditCompleted.svg',
                                    iconImageCompletedDisabled: 'verazial-common-frontend/assets/images/menu/EditCompletedDisabled.svg',
                                    iconImageDisabled: 'verazial-common-frontend/assets/images/menu/EditPendingDisabled.svg',
                                    routerLink: ['/general/enroll/continued'],
                                    routerLinks: ['/general/enroll/continued']
                                });
                                new_subject_menu.items!.push({
                                    label: 'menu.summary',
                                    accessCode: AccessIdentifier.ENROLL_WIZARD,
                                    iconImage: 'verazial-common-frontend/assets/images/menu/EditSummary.svg',
                                    iconImageActive: 'verazial-common-frontend/assets/images/menu/EditSummaryActive.svg',
                                    iconImageDisabled: 'verazial-common-frontend/assets/images/menu/EditSummaryDisabled.svg',
                                    routerLink: ['/general/enroll/summary'],
                                    routerLinks: ['/general/enroll/summary']
                                });
                                subject_menu.items.push(new_subject_menu);
                            }
                            this.model.push(
                                subject_menu,
                                {
                                    separator: true,
                                });
                        }
                        if (this.checkPermissionsService.hasReadPermissions(AccessIdentifier.ADMINISTRATION)) {
                            const administration_menu = {
                                label: '',
                                accessCode: AccessIdentifier.ADMINISTRATION,
                                items: [{}],
                            }

                            if (this.checkPermissionsService.hasReadPermissions(AccessIdentifier.TENANTS)) administration_menu.items.push(
                                {
                                    label: 'menu.tenants',
                                    accessCode: AccessIdentifier.TENANTS,
                                    icon: 'pi pi-cloud',
                                    routerLink: ['/tenants'],
                                    routerLinks: ['/tenants']
                                }
                            );
                            if (this.checkPermissionsService.hasReadPermissions(AccessIdentifier.ROLES)) administration_menu.items.push(
                                {
                                    label: 'menu.profile_role',
                                    accessCode: AccessIdentifier.ROLES,
                                    icon: 'pi pi-sitemap',
                                    routerLink: ['/roles'],
                                    routerLinks: ['/roles']
                                }
                            );
                            if (this.checkPermissionsService.hasReadPermissions(AccessIdentifier.USER)) administration_menu.items.push(
                                {
                                    label: 'menu.users',
                                    accessCode: AccessIdentifier.USER,
                                    iconImage: 'verazial-common-frontend/assets/images/menu/Users.svg',
                                    iconImageActive: 'verazial-common-frontend/assets/images/menu/UsersActive.svg',
                                    iconImageDisabled: 'verazial-common-frontend/assets/images/menu/UsersDisabled.svg',
                                    routerLink: ['/general/user'],
                                    routerLinks: ['/general/user', '/general/user/edit']
                                },
                            );
                            if (this.checkPermissionsService.hasReadPermissions(AccessIdentifier.ADMIN_LOCATIONS)) administration_menu.items.push(
                                {
                                    label: 'menu.locations',
                                    accessCode: AccessIdentifier.ADMIN_LOCATIONS,
                                    iconImage: 'verazial-common-frontend/assets/images/menu/Locations.svg',
                                    iconImageActive: 'verazial-common-frontend/assets/images/menu/LocationsActive.svg',
                                    iconImageDisabled: 'verazial-common-frontend/assets/images/menu/LocationsDisabled.svg',
                                    routerLink: ['/admin-locations'],
                                    routerLinks: ['/admin-locations']
                                }
                            );
                            if (managerSettings?.continued1?.criminalisticsSettings?.isCriminalisticsEnabled && this.checkPermissionsService.hasReadAndWritePermissions(AccessIdentifier.CRIMINALISTICS)) {
                                const criminalistics_menu = {
                                    label: 'menu.criminalistics',
                                    accessCode: AccessIdentifier.PRISONS_ADD_ON,
                                    iconImage: 'verazial-common-frontend/assets/images/menu/Criminalistics.svg',
                                    iconImageActive: 'verazial-common-frontend/assets/images/menu/CriminalisticsActive.svg',
                                    iconImageDisabled: 'verazial-common-frontend/assets/images/menu/CriminalisticsDisabled.svg',
                                    items: [{}],
                                }
                                if (this.checkPermissionsService.hasReadAndWritePermissions(AccessIdentifier.CRIMINALISTICS_CASES)) criminalistics_menu.items.push(
                                    {
                                        label: 'menu.criminalistics_cases',
                                        accessCode: AccessIdentifier.CRIMINALISTICS_CASES,
                                        iconImage: 'verazial-common-frontend/assets/images/menu/Cases.svg',
                                        iconImageActive: 'verazial-common-frontend/assets/images/menu/CasesActive.svg',
                                        iconImageDisabled: 'verazial-common-frontend/assets/images/menu/CasesDisabled.svg',
                                        routerLink: ['/criminalistics/cases'],
                                        routerLinks: ['/criminalistics/cases', '/criminalistics/cases/detail']
                                    }
                                );
                                if (this.checkPermissionsService.hasReadAndWritePermissions(AccessIdentifier.CRIMINALISTICS_REPORTS)) criminalistics_menu.items.push(
                                    {
                                        label: 'menu.criminalistics_reports',
                                        accessCode: AccessIdentifier.CRIMINALISTICS_REPORTS,
                                        iconImage: 'verazial-common-frontend/assets/images/menu/Report.svg',
                                        iconImageActive: 'verazial-common-frontend/assets/images/menu/ReportActive.svg',
                                        iconImageDisabled: 'verazial-common-frontend/assets/images/menu/ReportDisabled.svg',
                                        routerLink: ['/criminalistics/reports'],
                                        routerLinks: ['/criminalistics/reports']
                                    }
                                );
                                administration_menu.items.push(criminalistics_menu);
                            }
                            if (managerSettings?.continued1?.prisonsSettings?.isPrisonsEnabled && this.checkPermissionsService.hasReadPermissions(AccessIdentifier.PRISONS_ADD_ON)) {
                                const prisons_menu = {
                                    label: 'menu.prisons',
                                    accessCode: AccessIdentifier.PRISONS_ADD_ON,
                                    iconImage: 'verazial-common-frontend/assets/images/menu/Prisons.svg',
                                    iconImageActive: 'verazial-common-frontend/assets/images/menu/PrisonsActive.svg',
                                    iconImageDisabled: 'verazial-common-frontend/assets/images/menu/PrisonsDisabled.svg',
                                    items: [{}],
                                }
                                if (this.checkPermissionsService.hasReadPermissions(AccessIdentifier.PRISONS_VISITS)) prisons_menu.items.push(
                                    {
                                        label: 'menu.prisons_schedules',
                                        accessCode: AccessIdentifier.PRISONS_VISITS,
                                        icon: 'pi pi-calendar',
                                        routerLink: ['/prisons/visits'],
                                        routerLinks: ['/prisons/visits']
                                    }
                                );
                                if (this.checkPermissionsService.hasReadPermissions(AccessIdentifier.PRISONS_TRANSFERS)) prisons_menu.items.push(
                                    {
                                        label: 'menu.prisons_transfers',
                                        accessCode: AccessIdentifier.PRISONS_TRANSFERS,
                                        icon: 'pi pi-truck',
                                        routerLink: ['/prisons/transfers'],
                                        routerLinks: ['/prisons/transfers']
                                    }
                                );
                                administration_menu.items.push(prisons_menu);
                            }
                            if (managerSettings?.continued1?.clockSettings?.isClockEnabled && this.checkPermissionsService.hasReadPermissions(AccessIdentifier.PROCESS)) {
                                const process_menu = {
                                    label: 'menu.clock',
                                    accessCode: AccessIdentifier.PROCESS,
                                    icon: 'pi pi-clock',
                                    items: [{}],
                                }
                                if (this.checkPermissionsService.hasReadPermissions(AccessIdentifier.FLOW)) process_menu.items.push(
                                    {
                                        label: 'menu.flows',
                                        accessCode: AccessIdentifier.FLOW,
                                        icon: 'pi pi-share-alt',
                                        routerLink: ['/process/flow'],
                                        routerLinks: ['/process/flow']
                                    }
                                );
                                if (this.checkPermissionsService.hasReadPermissions(AccessIdentifier.CATEGORIES)) process_menu.items.push(
                                    {
                                        label: 'menu.categories',
                                        accessCode: AccessIdentifier.CATEGORIES,
                                        icon: 'pi pi-folder',
                                        routerLink: ['/process/categories'],
                                        routerLinks: ['/process/categories']
                                    },
                                );
                                if (this.checkPermissionsService.hasReadPermissions(AccessIdentifier.ASSIGNMENTS)) process_menu.items.push(
                                    {
                                        label: 'menu.assignments',
                                        accessCode: AccessIdentifier.ASSIGNMENTS,
                                        icon: 'pi pi-th-large',
                                        routerLink: ['/process/assignments'],
                                        routerLinks: ['/process/assignments']
                                    }
                                );
                                administration_menu.items.push(process_menu);
                            }

                            if ( managerSettings?.continued1?.passSettings?.isPassEnabled && this.checkPermissionsService.hasReadPermissions(AccessIdentifier.PASS_CONFIGURATION)) {
                                const process_menu = {
                                    label: 'menu.pass',
                                    accessCode: AccessIdentifier.PASS_CONFIGURATION,
                                    iconImage: 'verazial-common-frontend/assets/images/menu/Pass.svg',
                                    iconImageActive: 'verazial-common-frontend/assets/images/menu/PassActive.svg',
                                    iconImageDisabled: 'verazial-common-frontend/assets/images/menu/PassDisabled.svg',
                                    items: [{}],
                                }
                                if (this.checkPermissionsService.hasReadPermissions(AccessIdentifier.PASS_DATASOURCE)) process_menu.items.push(
                                    {
                                        label: 'menu.application_datasource',
                                        accessCode: AccessIdentifier.PASS_DATASOURCE,
                                        icon: 'pi pi-database',
                                        routerLink: ['/pass/data-sources'],
                                        routerLinks: ['/pass/data-sources']
                                    }
                                );
                                if (this.checkPermissionsService.hasReadPermissions(AccessIdentifier.PASS_APPS) && this.checkPermissionsService.hasReadPermissions(AccessIdentifier.PASS_APPS_MANAGEMENT)) process_menu.items.push(
                                    {
                                        label: 'menu.applications',
                                        accessCode: AccessIdentifier.PASS_APPS_MANAGEMENT,
                                        icon: 'pi pi-desktop',
                                        routerLink: ['/pass/applications/apps'],
                                        routerLinks: ['/pass/applications/apps']
                                    },
                                );
                                if (this.checkPermissionsService.hasReadPermissions(AccessIdentifier.PASS_APP_FLOWS)) process_menu.items.push(
                                    {
                                        label: 'menu.application_flow',
                                        accessCode: AccessIdentifier.PASS_APP_FLOWS,
                                        // icon: 'pi pi-file',
                                        iconImage: 'verazial-common-frontend/assets/images/menu/Process.svg',
                                        iconImageActive: 'verazial-common-frontend/assets/images/menu/ProcessActive.svg',
                                        iconImageDisabled: 'verazial-common-frontend/assets/images/menu/ProcessDisabled.svg',
                                        routerLink: ['/pass/app-flows'],
                                        routerLinks: ['/pass/app-flows']
                                    },
                                );
                                if (this.checkPermissionsService.hasReadPermissions(AccessIdentifier.PASS_APPS) && this.checkPermissionsService.hasReadPermissions(AccessIdentifier.PASS_ASSIGN_APP)) process_menu.items.push(
                                    {
                                        label: 'menu.application_assign',
                                        accessCode: AccessIdentifier.ASSIGNMENTS,
                                        icon: 'pi pi-list',
                                        routerLink: ['/pass/applications/assign'],
                                        routerLinks: ['/pass/applications/assign']
                                    }
                                );

                                administration_menu.items.push(process_menu);
                            }

                            /*if (this.checkPermissionsService.hasReadPermissions(AccessIdentifier.TIME_RECORDS)) administration_menu.items.push(
                                {
                                    label: 'menu.time_records',
                                    accessCode: AccessIdentifier.TIME_RECORDS,
                                    iconImage: 'verazial-common-frontend/assets/images/menu/CalendarCheck.svg',
                                    iconImageActive: 'verazial-common-frontend/assets/images/menu/CalendarCheckActive.svg',
                                    iconImageDisabled: 'verazial-common-frontend/assets/images/menu/CalendarCheckDisabled.svg',
                                    routerLink: ['/actions/time-records/signatures'],
                                    routerLinks: ['/actions/time-records/signatures']
                                }
                            );*/
                            this.model.push(
                                administration_menu,
                                {
                                    separator: true,
                                }
                            );
                        }
                        if (this.checkPermissionsService.hasReadPermissions(AccessIdentifier.REPORTS)) {
                            const reports_section = {
                                label: '',
                                accessCode: AccessIdentifier.REPORTS,
                                items: [{}],
                            }

                            const reports_menu = {
                                label: 'menu.reports',
                                accessCode: AccessIdentifier.REPORTS_MENU,
                                iconImage: 'verazial-common-frontend/assets/images/menu/Reports.svg',
                                iconImageActive: 'verazial-common-frontend/assets/images/menu/ReportsActive.svg',
                                iconImageDisabled: 'verazial-common-frontend/assets/images/menu/ReportsDisabled.svg',
                                items: [{}],
                            }
                            /*if (this.checkPermissionsService.hasReadPermissions(AccessIdentifier.REPORTS_GENERAL)) reports_menu.items.push(
                                {
                                    label: 'menu.reports_general',
                                    accessCode: AccessIdentifier.REPORTS_GENERAL,
                                    iconImage: 'verazial-common-frontend/assets/images/menu/Reports.svg',
                                    iconImageActive: 'verazial-common-frontend/assets/images/menu/ReportsActive.svg',
                                    iconImageDisabled: 'verazial-common-frontend/assets/images/menu/ReportsDisabled.svg',
                                    routerLink: ['/reports/general'],
                                    routerLinks: ['/reports/general']
                                }
                            );

                            if (this.checkPermissionsService.hasReadPermissions(AccessIdentifier.REPORTS_USERS)) reports_menu.items.push(
                                {
                                    label: 'menu.reports_users',
                                    accessCode: AccessIdentifier.REPORTS_USERS,
                                    iconImage: 'verazial-common-frontend/assets/images/menu/ReportsUsers.svg',
                                    iconImageActive: 'verazial-common-frontend/assets/images/menu/ReportsUsersActive.svg',
                                    iconImageDisabled: 'verazial-common-frontend/assets/images/menu/ReportsUsersDisabled.svg',
                                    routerLink: ['/reports/users'],
                                    routerLinks: ['/reports/users']
                                }
                            );
                            if (this.checkPermissionsService.hasReadPermissions(AccessIdentifier.REPORTS_SUBJECTS)) reports_menu.items.push(
                                {
                                    label: 'menu.reports_subjects',
                                    accessCode: AccessIdentifier.REPORTS_SUBJECTS,
                                    iconImage: 'verazial-common-frontend/assets/images/menu/ReportsSubjects.svg',
                                    iconImageActive: 'verazial-common-frontend/assets/images/menu/ReportsSubjectsActive.svg',
                                    iconImageDisabled: 'verazial-common-frontend/assets/images/menu/ReportsSubjectsDisabled.svg',
                                    routerLink: ['/reports/subjects'],
                                    routerLinks: ['/reports/subjects']
                                }
                            );*/
                            if (this.checkPermissionsService.hasReadPermissions(AccessIdentifier.REPORTS_VERIFICATION)) reports_menu.items.push(
                                {
                                    label: 'menu.reports_verification',
                                    accessCode: AccessIdentifier.REPORTS_VERIFICATION,
                                    iconImage: 'verazial-common-frontend/assets/images/menu/ReportsVerification.svg',
                                    iconImageActive: 'verazial-common-frontend/assets/images/menu/ReportsVerificationActive.svg',
                                    iconImageDisabled: 'verazial-common-frontend/assets/images/menu/ReportsVerificationDisabled.svg',
                                    routerLink: ['/reports/verification'],
                                    routerLinks: ['/reports/verification']
                                }
                            );
                            if (this.checkPermissionsService.hasReadPermissions(AccessIdentifier.REPORTS_IDENTIFICATION)) reports_menu.items.push(
                                {
                                    label: 'menu.reports_identification',
                                    accessCode: AccessIdentifier.REPORTS_IDENTIFICATION,
                                    iconImage: 'verazial-common-frontend/assets/images/menu/ReportsIdentification.svg',
                                    iconImageActive: 'verazial-common-frontend/assets/images/menu/ReportsIdentificationActive.svg',
                                    iconImageDisabled: 'verazial-common-frontend/assets/images/menu/ReportsIdentificationDisabled.svg',
                                    routerLink: ['/reports/identification'],
                                    routerLinks: ['/reports/identification']
                                }
                            );
                            if (this.checkPermissionsService.hasReadPermissions(AccessIdentifier.REPORTS_PERFORMANCE)) reports_menu.items.push(
                                {
                                    label: 'menu.reports_performance',
                                    accessCode: AccessIdentifier.REPORTS_PERFORMANCE,
                                    iconImage: 'verazial-common-frontend/assets/images/menu/ReportsPerformance.svg',
                                    iconImageActive: 'verazial-common-frontend/assets/images/menu/ReportsPerformanceActive.svg',
                                    iconImageDisabled: 'verazial-common-frontend/assets/images/menu/ReportsPerformanceDisabled.svg',
                                    routerLink: ['/reports/performance'],
                                    routerLinks: ['/reports/performance']
                                }
                            );
                            if (this.checkPermissionsService.hasReadPermissions(AccessIdentifier.REPORTS_ACTIONS)) reports_menu.items.push(
                                {
                                    label: 'menu.reports_actions',
                                    accessCode: AccessIdentifier.REPORTS_ACTIONS,
                                    iconImage: 'verazial-common-frontend/assets/images/menu/ReportsActions.svg',
                                    iconImageActive: 'verazial-common-frontend/assets/images/menu/ReportsActionsActive.svg',
                                    iconImageDisabled: 'verazial-common-frontend/assets/images/menu/ReportsActionsDisabled.svg',
                                    routerLink: ['/reports/actions'],
                                    routerLinks: ['/reports/actions']
                                }
                            );
                            if (this.checkPermissionsService.hasReadPermissions(AccessIdentifier.REPORTS_AUDIT_TRAIL)) reports_menu.items.push(
                                {
                                    label: 'menu.reports_audit_trail',
                                    accessCode: AccessIdentifier.REPORTS_AUDIT_TRAIL,
                                    iconImage: 'verazial-common-frontend/assets/images/menu/ReportsAuditTrail.svg',
                                    iconImageActive: 'verazial-common-frontend/assets/images/menu/ReportsAuditTrailActive.svg',
                                    iconImageDisabled: 'verazial-common-frontend/assets/images/menu/ReportsAuditTrailDisabled.svg',
                                    routerLink: ['/reports/audit-trail'],
                                    routerLinks: ['/reports/audit-trail']
                                }
                            );

                            reports_section.items.push(reports_menu);


                            this.model.push(reports_section);
                        }
                    },
                    (e) => {
                        this.loggerService.error('Error Getting Settings By Application:');
                        this.loggerService.error(e);
                    }
                );
            },
            error: (e) => {
                this.loggerService.error('Error Getting Konektor Properties:');
                this.loggerService.error(e);
            }
        });
    }

    openSelector() {
        this.showSelectOptionDialog = true;
    }
}
