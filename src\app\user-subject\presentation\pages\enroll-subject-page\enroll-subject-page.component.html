<p-toast></p-toast>
<p-confirmDialog />
<app-loading-spinner [isLoading]="isLoading"></app-loading-spinner>
<div *ngIf="step != 'summary'">
    <app-user-not-verified [userIsVerified]="userIsVerified" [subjectType]="isUser ? '_user' : ''" [verifyReady]="true" (modified)="userVerifying($event)"></app-user-not-verified>
</div>
<div class="flex flex-column align-items-center justify-content-center w-full">
    @switch (step) {
        @case ('new') {
            <div class="card flex flex-column justify-content-center">
                <p-steps [model]="items" [readonly]="true" [activeIndex]="activeIndex" [style]="{'width': '75vw'}"></p-steps>
                @switch (activeIndex) {
                    @case (0) {
                        <div class="p-fluid">
                            <div class="flex justify-content-center">
                                <app-edit-user-subject
                                    [readAndWritePermissions]="readAndWritePermissions"
                                    [userSubject]="subjectDataEdit"
                                    [isUser]="isUser"
                                    [isVerified]="true"
                                    [userIsVerified]="userIsVerified"
                                    [actionType]="actionType"
                                    (outputData)="onSubjectChanged($event)"
                                    (picture)="onSubmitSubjectPicture($event)"
                                    (allowSave)="allowSave($event)"
                                ></app-edit-user-subject>
                            </div>
                            <!-- <div class="flex align-items-center justify-content-end w-full">
                                <p-button *ngIf="readAndWritePermissions" pRipple [style]="{'background': '#009BA9', 'border-color': '#009BA9'}" class="m-1"
                                    [disabled]="isDisableSaveButton"
                                    label="{{'save' | translate}}" (click)="onSubmitSubject()"></p-button>
                            </div> -->
                        </div>
                        <div class="flex pt-4 align-items-center justify-content-end gap-2">
                            <p-button
                                pRipple
                                [style]="{'width':'101px','height':'38px', 'color': '#64748B' , 'border': 'none', 'background': '#FFFFFF', 'font-family': 'Open Sans', 'font-size': '14px'}"
                                label="{{'cancel' | translate}}" (click)="navigateHome()"></p-button>
                            <p-button
                                label="{{ 'next' | translate }}"
                                [style]="{'pointer-events': 'auto', 'width':'101px','height':'38px', 'color': '#FFFFFF' , 'border': '1px solid #204887', 'background': '#204887', 'font-family': 'Open Sans', 'font-size': '14px'}"
                                [disabled]="isDisableSaveButton || !saveAllowed"
                                pTooltip="{{ (!saveAllowed) ? ('messages.cannot_' + ((actionType == userSubjectActionTypes.CREATE) ? 'create' : ((actionType == userSubjectActionTypes.UPDATE) ? 'update': 'save')) + '_subject_user_ds' | translate) : '' }}" tooltipPosition="top"
                                (onClick)="onSubmitSubject()"/>
                        </div>
                    }
                    @case (showExtendedBioFieldsTab ? 1 : -1) {
                        <div class="flex justify-content-center mt-2">
                            <!-- Dynamic Form -->
                            <ng-container *ngTemplateOutlet="dynamicFormContentContainer" />
                            <div *ngIf="subjectData==undefined">
                                <ng-container *ngTemplateOutlet="noData" />
                            </div>
                        </div>
                    }
                    @case (showJudicialFileTab ? 1 + (showExtendedBioFieldsTab ? 1 : 0) : -1) {
                        <div *ngIf="subjectData!=undefined else noData" class="flex justify-content-center mt-2">
                            <app-judicial-files-list
                                [userSubject]="subjectData"
                                [userIsVerified]="userIsVerified"
                                (contentModified)="updateModified($event)"
                            ></app-judicial-files-list>
                        </div>
                        <div *ngIf="subjectData!=undefined" class="flex pt-4 align-items-center justify-content-end gap-2">
                            <p-button
                                label="{{ 'back' | translate }}"
                                [style]="{'width':'101px','height':'38px', 'color': '#64748B' , 'border': 'none', 'background': '#FFFFFF', 'font-family': 'Open Sans', 'font-size': '14px'}"
                                (onClick)="updateNewInternalStep(activeIndex - 1)"/>
                            <p-button
                                label="{{ 'next' | translate }}"
                                [style]="{'width':'101px','height':'38px', 'color': '#FFFFFF' , 'border': 'none', 'background': '#204887', 'font-family': 'Open Sans', 'font-size': '14px'}"
                                [disabled]="isDisableContinueButton"
                                (onClick)="updateNewInternalStep(activeIndex + 1)"/>
                        </div>
                    }
                    @case (showPhysicalDataTab ? 1 + (showExtendedBioFieldsTab ? 1 : 0) + (showJudicialFileTab ? 1 : 0) : -1) {
                        <div *ngIf="subjectData!=undefined else noData" class="flex justify-content-center mt-2">
                            <div *ngIf="userIsVerified else verifyUser" class="w-11">
                                <app-physical-data
                                    [userSubject]="subjectData"
                                    [canReadAndWrite]="readAndWritePermissions"
                                    [isUser]="isUser"
                                    [userIsVerified]="userIsVerified"
                                    [subjectIsVerified]="true"
                                ></app-physical-data>
                            </div>
                        </div>
                        <div *ngIf="subjectData!=undefined" class="flex pt-4 align-items-center justify-content-end gap-2">
                            <p-button
                                label="{{ 'back' | translate }}"
                                [style]="{'width':'101px','height':'38px', 'color': '#64748B' , 'border': 'none', 'background': '#FFFFFF', 'font-family': 'Open Sans', 'font-size': '14px'}"
                                (onClick)="updateNewInternalStep(activeIndex - 1)"/>
                            <p-button
                                label="{{ 'continue' | translate }}"
                                [style]="{'width':'101px','height':'38px', 'color': '#FFFFFF' , 'border': 'none', 'background': '#204887', 'font-family': 'Open Sans', 'font-size': '14px'}"
                                [disabled]="isDisableContinueButton"
                                (onClick)="updateNewInternalStep(activeIndex + 1)"/>
                        </div>
                    }
                }
                <!-- <p-stepper [activeStep]="activeIndex" (activeStepChange)="updateNewInternalStep($event)">
                    <p-stepperPanel header="{{ 'titles.biographic_data' | translate }}">
                        <ng-template pTemplate="header" let-onClick="onClick" let-index="index">
                            <button class="p-stepper-action bg-transparent border-none flex align-items-center justify-content-center gap-2" (click)="onClick.emit()">
                                <span
                                    class="p-stepper-number border-circle w-2rem h-2rem inline-flex align-items-center justify-content-center"
                                    [ngClass]="{
                                        'active-step': index == activeIndex,
                                        'checked-step': index < activeIndex,
                                        'inactive-step': index > activeIndex,
                                    }"
                                >
                                @if(index >= activeIndex){
                                    {{index + 1}}
                                }@else{
                                    <i class="pi pi-check"></i>
                                }
                                </span>
                                <div class="p-stepper-title">{{ 'titles.biographic_data' | translate }}</div>
                            </button>
                        </ng-template>
                        <ng-template pTemplate="content" let-nextCallback="nextCallback" let-index="index"></ng-template>
                    </p-stepperPanel>
                    <p-stepperPanel header="{{ 'titles.extended_biographic_data' | translate}}">
                        <ng-template pTemplate="header" let-onClick="onClick" let-index="index">
                            <button class="p-stepper-action bg-transparent border-none flex align-items-center justify-content-center gap-2" (click)="onClick.emit()">
                                <span
                                    class="p-stepper-number border-circle w-2rem h-2rem inline-flex align-items-center justify-content-center"
                                    [ngClass]="{
                                        'active-step': index == activeIndex,
                                        'checked-step': index < activeIndex,
                                        'inactive-step': index > activeIndex,
                                    }"
                                >
                                @if(index >= activeIndex){
                                    {{index + 1}}
                                }@else{
                                    <i class="pi pi-check"></i>
                                }
                                </span>
                                <div class="p-stepper-title">{{ 'titles.extended_biographic_data' | translate }}</div>
                            </button>
                        </ng-template>
                        <ng-template pTemplate="content" let-prevCallback="prevCallback" let-nextCallback="nextCallback" let-index="index"></ng-template>
                    </p-stepperPanel>
                    <p-stepperPanel header="{{ 'titles.physical_data' | translate}}">
                        <ng-template pTemplate="header" let-onClick="onClick" let-index="index">
                            <button class="p-stepper-action bg-transparent border-none flex align-items-center justify-content-center gap-2" (click)="onClick.emit()">
                                <span
                                    class="p-stepper-number border-circle w-2rem h-2rem inline-flex align-items-center justify-content-center"
                                    [ngClass]="{
                                        'active-step': index == activeIndex,
                                        'checked-step': index < activeIndex,
                                        'inactive-step': index > activeIndex,
                                    }"
                                >
                                @if(index >= activeIndex){
                                    {{index + 1}}
                                }@else{
                                    <i class="pi pi-check"></i>
                                }
                                </span>
                                <div class="p-stepper-title">{{ 'titles.physical_data' | translate }}</div>
                            </button>
                        </ng-template>
                        <ng-template pTemplate="content" let-prevCallback="prevCallback" let-index="index"></ng-template>
                    </p-stepperPanel>
                </p-stepper> -->
            </div>
            <!-- <div class="flex align-items-center justify-content-end w-11 mt-3">
                <p-button *ngIf="readAndWritePermissions" pRipple [style]="{'background': '#009BA9', 'border-color': '#009BA9'}" class="m-1"
                    [disabled]="isDisableContinueButton"
                    label="{{'continue' | translate}}" (click)="onSubmitContinue()"></p-button>
                <p-button pRipple severity="secondary" class="m-1"
                    label="{{'cancel' | translate}}" (click)="navigateHome()"></p-button>
            </div> -->
        }
        @case ('fingerprint') {
            <p-card [className]="'w-full mb-3'">
                <app-user-subject-card
                    [readAndWritePermissions]="readAndWritePermissions"
                    [userSubject]="subjectData"
                    [showButtons]="false"
                ></app-user-subject-card>
            </p-card>
            <div class="col-12 flex align-items-center justify-content-center">
                <div class="flex gap-2 flex-column">
                <label for="fileInput">{{ 'content.uploadFile' | translate}}</label>
                <p-fileUpload
                    [customUpload]="true"
                    chooseLabel="{{ 'content.selectImage' | translate }}"
                    accept="image/*"
                    (uploadHandler)="onFileSelected($event)"
                    (onClear)="onFileCleared()"
                    (onRemove)="onFileCleared()"
                    [disabled]="inProgress"
                />
                </div>
            </div>
            <lib-ui-fingerprint-detector
                *ngIf="!hideUI && showFileEnroll"
                [input]="input"
                [backend]="backend"
                [confidenceThreshold]="confidenceThreshold"
                [boxColor]="boxColor"
                [showTestUI]="showTestUI"
                [showInferenceTime]="showInferenceTime"
                [showExtractions]="showExtractions"
                [showDownloadButtons]="showDownloadButtons"
                (inProgress)="inProgressChanged($event)"
                (inferenceComplete)="onFingerprintInference($event)"
                (extractionComplete)="onFingerprintDetection($event)"
                (error)="onFingerprintDetectionError($event)"
            ></lib-ui-fingerprint-detector>
            <p-table
                [value]="fingerprintExtractionsArray"
                dataKey="index"
                [tableStyle]="{ 'min-width': '50rem' }"
            >
                    <ng-template pTemplate="header">
                        <tr>
                            <th style="width:25%">
                                Index
                            </th>
                            <th style="width:25%">
                                Fingerprint
                            </th>
                            <th style="width:25%">
                                Sample Type
                            </th>
                            <th style="width:25%">
                                Options
                            </th>
                        </tr>
                    </ng-template>
                    <ng-template pTemplate="body" let-detections let-rowIndex="rowIndex" let-editing="editing">
                        <tr>
                            <td>
                                {{rowIndex}}
                            </td>
                            <td>
                                <img [src]="detections.image" style="max-height: 100px;" alt="Fingerprint">
                            </td>
                            <td></td>
                            <td></td>
                        </tr>
                    </ng-template>
            </p-table>
            <ng-container *ngTemplateOutlet="widget" />
            <div class="flex align-items-center justify-content-end gap-2 w-11 mt-3">
                <p-button
                    *ngIf="getAdjacentSteps(step).previous != 'new'"
                    label="{{ 'back' | translate }}"
                    [style]="{'width':'101px','height':'38px', 'color': '#64748B' , 'border': 'none', 'background': '#FFFFFF', 'font-family': 'Open Sans', 'font-size': '14px'}"
                    (onClick)="onSubmitBack()"/>
                <p-button
                    label="{{ 'next' | translate }}"
                    [style]="{'width':'101px','height':'38px', 'color': '#FFFFFF' , 'border': 'none', 'background': '#204887', 'font-family': 'Open Sans', 'font-size': '14px'}"
                    [disabled]="isDisableContinueButton"
                    (onClick)="onSubmitContinue()"/>
            </div>
        }
        @case ('continued') {
            <p-steps [model]="items" [readonly]="true" [activeIndex]="activeIndex" [style]="{'width': '75vw'}"></p-steps>
            <p-card [className]="'w-full mt-2'">
                <app-user-subject-card
                    [readAndWritePermissions]="readAndWritePermissions"
                    [userSubject]="subjectData"
                    [showButtons]="false"
                ></app-user-subject-card>
            </p-card>
            <div class="w-full pb-2">
                <ng-container *ngTemplateOutlet="widget; context: { isHidden: true }"></ng-container>
                @switch (activeIndex) {
                    @case(this.showLocationsTab ? 0 : -1) {
                        <app-user-locations
                            [userSubject]="subjectData"
                            [isPrisoner]="isPrisoner()"
                            [userIsVerified]="userIsVerified"
                            (updateSubjectLocation)="updateSegmentedAttributes()"
                        ></app-user-locations>
                        <div *ngIf="subjectData!=undefined" class="flex pt-4 align-items-center justify-content-end mr-5 gap-2">
                            <p-button
                                label="{{ 'back' | translate }}"
                                [style]="{'width':'101px','height':'38px', 'color': '#64748B' , 'border': 'none', 'background': '#FFFFFF', 'font-family': 'Open Sans', 'font-size': '14px'}"
                                (onClick)="updateNewInternalStep(activeIndex - 1)"/>
                            <p-button
                                label="{{ 'next' | translate }}"
                                [style]="{'width':'101px','height':'38px', 'color': '#FFFFFF' , 'border': 'none', 'background': '#204887', 'font-family': 'Open Sans', 'font-size': '14px'}"
                                [disabled]="isDisableContinueButton"
                                (onClick)="updateNewInternalStep(activeIndex + 1)"/>
                        </div>
                    }
                    @case(this.showEntriesExitsTab ? 0 + (this.showLocationsTab ? 1 : 0) : -1) {
                        <app-entries-exits
                            [userSubject]="subjectData"
                            [userIsVerified]="userIsVerified"
                            [isVerified]="biometricSamples"
                            [userSubjectDetails]="userSubjectDetails"
                            [isPrisoner]="isPrisoner()"
                            [prisonsConfig]="prisonsConfig"
                            [managerSettings]="managerSettings"
                            [konektorProperties]="konektorProperties"
                            (updateSubjectStatus)="updateSegmentedAttributes()"
                        ></app-entries-exits>
                        <div *ngIf="subjectData!=undefined" class="flex pt-4 align-items-center justify-content-end mr-5 gap-2">
                            <p-button
                                label="{{ 'back' | translate }}"
                                [style]="{'width':'101px','height':'38px', 'color': '#64748B' , 'border': 'none', 'background': '#FFFFFF', 'font-family': 'Open Sans', 'font-size': '14px'}"
                                (onClick)="updateNewInternalStep(activeIndex - 1)"/>
                            <p-button
                                label="{{ 'next' | translate }}"
                                [style]="{'width':'101px','height':'38px', 'color': '#FFFFFF' , 'border': 'none', 'background': '#204887', 'font-family': 'Open Sans', 'font-size': '14px'}"
                                [disabled]="isDisableContinueButton"
                                (onClick)="updateNewInternalStep(activeIndex + 1)"/>
                        </div>
                    }
                    @case(this.showBelongingsTab ? 0 + (this.showLocationsTab ? 1 : 0) + (this.showEntriesExitsTab ? 1 : 0) : -1) {
                        <app-belongings-list
                            [userSubject]="subjectData"
                            [userIsVerified]="userIsVerified"
                        ></app-belongings-list>
                        <div *ngIf="subjectData!=undefined" class="flex pt-4 align-items-center justify-content-end mr-5 gap-2">
                            <p-button
                                label="{{ 'back' | translate }}"
                                [style]="{'width':'101px','height':'38px', 'color': '#64748B' , 'border': 'none', 'background': '#FFFFFF', 'font-family': 'Open Sans', 'font-size': '14px'}"
                                (onClick)="updateNewInternalStep(activeIndex - 1)"/>
                            <p-button
                                label="{{ 'next' | translate }}"
                                [style]="{'width':'101px','height':'38px', 'color': '#FFFFFF' , 'border': 'none', 'background': '#204887', 'font-family': 'Open Sans', 'font-size': '14px'}"
                                [disabled]="isDisableContinueButton"
                                (onClick)="updateNewInternalStep(activeIndex + 1)"/>
                        </div>
                    }
                }
            </div>
        }
        @case ('summary') {
            <div class="w-full">
                <app-user-subject-edit-page
                    [isChild]="true"
                    [userIsVerified]="userIsVerified"
                    [verified]="true"
                    [numId]="nId"
                ></app-user-subject-edit-page>
            </div>
        }
        @default {
            <p-card [className]="'w-full mb-3'">
                <app-user-subject-card
                    [readAndWritePermissions]="readAndWritePermissions"
                    [userSubject]="subjectData"
                    [showButtons]="false"
                ></app-user-subject-card>
            </p-card>
            <ng-container *ngTemplateOutlet="widget" />
            <div class="flex align-items-center justify-content-end gap-2 w-11 mt-3">
                <p-button
                    *ngIf="getAdjacentSteps(step).previous != 'new'"
                    label="{{ 'back' | translate }}"
                    [style]="{'width':'101px','height':'38px', 'color': '#64748B' , 'border': 'none', 'background': '#FFFFFF', 'font-family': 'Open Sans', 'font-size': '14px'}"
                    (onClick)="onSubmitBack()"/>
                <p-button
                    label="{{ 'next' | translate }}"
                    [style]="{'width':'101px','height':'38px', 'color': '#FFFFFF' , 'border': 'none', 'background': '#204887', 'font-family': 'Open Sans', 'font-size': '14px'}"
                    [disabled]="isDisableContinueButton"
                    (onClick)="onSubmitContinue()"/>
            </div>
        }
    }
</div>

<div #dynamicFormContentContainer>
    <ng-template #dynamicFormContent></ng-template>
    <div *ngIf="subjectData!=undefined && showDynamicForm" class="flex pt-4 align-items-center justify-content-end gap-2">
        <p-button
            [style]="{'width':'101px','height':'38px', 'color': '#64748B' , 'border': 'none', 'background': '#FFFFFF', 'font-family': 'Open Sans', 'font-size': '14px'}"
            [disabled]="modified"
            label="{{ 'back' | translate }}"
            (onClick)="updateNewInternalStep(activeIndex - 1)"/>
        <p-button
            label="{{ 'next' | translate }}"
            [disabled]="modified"
            [style]="{'width':'101px','height':'38px', 'color': '#FFFFFF' , 'border': 'none', 'background': '#204887', 'font-family': 'Open Sans', 'font-size': '14px'}"
            (onClick)="updateNewInternalStep(activeIndex + 1)"/>
    </div>
</div>

<!-- Widget Enroll -->
<ng-template #widget>
    <app-widget-enroll
        [numId]="nId"
        [widgetUrl]="widgetUrl"
        [verified]="userIsVerified && readAndWritePermissions"
        [managerSettings]="managerSettings"
        [konektorProperties]="konektorProperties"
        [ready]="widgetReady"
        [isUser]="isUser"
        [updateAttributes]="updateAttributes"
        [singleTechEnroll]="singleTechEnroll"
        [tech]="tech"
        [samplesMode]="samplesMode"
        [subject]="subjectData"
        (result)="onWidgetEnrollResult($event)"
        [isHidden]="(isHidden ?? false) || hideUI"
    ></app-widget-enroll>
</ng-template>

<ng-template #noData>
    <div class="flex justify-content-center text-center m-5">
        {{ 'content.noDataAvailable' | translate }}
        <br>
        {{ 'content.createSubjectToContinue' | translate }}
    </div>
</ng-template>

<ng-template #verifyUser>
    <div class="flex justify-content-center text-center m-5">
        {{ 'content.noDataAvailable' | translate }}
        <br>
        {{ 'messages.verification_required_data' | translate }}
    </div>
</ng-template>

<p-dialog header="{{ 'content.selectOption' | translate }}" [modal]="true" [closable]="false" [(visible)]="showSelectOptionDialog">
    <ng-template pTemplate="content">
        <app-new-select-option
            [option1Label]="'content.enroll_live_samples'"
            [option2Label]="'content.enroll_file_samples'"
            (onOption1)="continueLive()"
            (onOption2)="continueFile()"
            (onCancel)="showSelectOptionDialog = false"
        ></app-new-select-option>
    </ng-template>
</p-dialog>