{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"verazial-app": {"projectType": "application", "schematics": {}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-builders/custom-webpack:browser", "options": {"customWebpackConfig": {"path": "./extra-webpack.config.js", "mergeStrategies": {"module.rules": "prepend"}}, "outputPath": "dist/verazial-app/browser", "index": "src/index.html", "main": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "assets": ["src/favicon.png", "src/assets", "src/assets/pkg", "src/verazial-common-frontend/assets"], "styles": ["src/styles.scss"], "scripts": ["node_modules/drawflow/dist/drawflow.min.js", "node_modules/mammoth/mammoth.browser.min.js"]}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "5mb", "maximumError": "10mb"}, {"type": "anyComponentStyle", "maximumWarning": "100kb", "maximumError": "600kb"}], "outputHashing": "all", "optimization": {"scripts": true, "styles": {"minify": true, "inlineCritical": false}, "fonts": true}}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.development.ts"}]}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"proxyConfig": "proxy.conf.json", "buildTarget": "move-safe:build"}, "configurations": {"production": {"buildTarget": "verazial-app:build:production"}, "development": {"buildTarget": "verazial-app:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "verazial-app:build"}}, "server": {"builder": "@angular-devkit/build-angular:server", "options": {"outputPath": "dist/verazial-app/server", "main": "server.ts", "tsConfig": "tsconfig.server.json"}, "configurations": {"production": {"outputHashing": "media"}, "development": {"buildOptimizer": false, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.development.ts"}], "optimization": false, "sourceMap": true, "extractLicenses": false, "vendorChunk": true}}, "defaultConfiguration": "production"}, "serve-ssr": {"builder": "@angular-devkit/build-angular:ssr-dev-server", "configurations": {"development": {"browserTarget": "verazial-app:build:development", "serverTarget": "verazial-app:server:development"}, "production": {"browserTarget": "verazial-app:build:production", "serverTarget": "verazial-app:server:production"}}, "defaultConfiguration": "development"}, "prerender": {"builder": "@angular-devkit/build-angular:prerender", "options": {"routes": ["/"]}, "configurations": {"production": {"browserTarget": "verazial-app:build:production", "serverTarget": "verazial-app:server:production"}, "development": {"browserTarget": "verazial-app:build:development", "serverTarget": "verazial-app:server:development"}}, "defaultConfiguration": "production"}}}}, "cli": {"analytics": "c3c77108-7be2-437e-808c-0ddd82dadc19"}}