import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { FileUploadHandlerEvent } from 'primeng/fileupload';
import { DashboardItemStructure } from 'src/verazial-common-frontend/modules/shared/components/dashboard/dashboard-item/dashboard-item/dashboard-item.component';

class KeyValue {
  key?: string;
  value?: string;
}

@Component({
  selector: 'app-home-page',
  templateUrl: './home-page.component.html',
  styleUrl: './home-page.component.css'
})
export class HomePageComponent implements OnInit {

  dashboardData: DashboardItemStructure = new DashboardItemStructure();

  /* Only for testing */
  @ViewChild('fileInput', { static: false }) fileInputRef!: ElementRef<HTMLInputElement>;
  inProgress: boolean = false;

  backends: KeyValue[] = [
    { key: 'ndarray', value: 'NDArray' },
    { key: 'webgpu', value: 'WebGPU' },
  ];

  input?: File;
  backend: string = this.backends[0].key!;
  confidenceThreshold: number = 0.7;
  boxColor: string = '#00FF00';
  showTestUI: boolean = true;
  showInferenceTime: boolean = true;
  showExtractions: boolean = true;
  showDownloadButtons: boolean = true;
  /* end */

  constructor() { }

  ngOnInit() {
    this.dashboardData = {
      serverDashboard: {
          serverDashboard: true,
          hardDrive: true,
          ram: true,
          biometricServerStatus: true,
          applicationsStatus: false,
      },
      administrationDashboard: {
          administrationDashboard: true,
          totalSubjects: true,
          totalUsers: true,
          totalIrisSamples: true,
          totalFingerprintSamples: true,
          totalFacialSamples: true,
          totalRolledFingerprintSamples: true,
          totalPalmSamples: true,
      },
      licensesDashboard: {
          licensesDashboard: false,
          totalLicenses: false,
          totalAvailableLicenses: false,
          totalAssignedLicenses: false,
          usedLicensesBreakdown: false,
      }
    };
  }
}
